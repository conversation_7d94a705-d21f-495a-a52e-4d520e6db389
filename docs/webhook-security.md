# Webhook Security Configuration

Este documento explica como configurar a segurança dos webhooks para Pagar.me e Mercado Pago.

## Variáveis de Ambiente Necessárias

### Pagar.me

```env
PAGARME_WEBHOOK_SECRET=seu_webhook_secret_do_pagarme
```

### Mercado <PERSON>go

```env
MERCADOPAGO_WEBHOOK_SECRET=seu_webhook_secret_do_mercadopago
```

## Como Obter os Webhook Secrets

### Pagar.me

1. Acesse o dashboard da Pagar.me
2. Vá em **Configurações** > **Webhooks**
3. Crie ou edite um webhook
4. Copie o **Secret** fornecido
5. Configure a URL do webhook: `https://seu-dominio.com/api/webhooks/pagarme`

### Mercado Pago

1. Acesse o painel do Mercado Pago
2. Vá em **Configurações** > **Webhooks**
3. Crie um novo webhook
4. Copie o **Secret** fornecido
5. Configure a URL do webhook: `https://seu-dominio.com/api/webhooks/mercadopago`

## Recursos de Segurança Implementados

### 1. Validação de Assinatura

- **Pagar.me**: Valida usando HMAC-SHA256 com o secret configurado
- **Mercado Pago**: Valida usando o formato específico do MP com HMAC-SHA256

### 2. Rate Limiting

- Limite de 100 requisições por minuto por IP
- Armazenamento em memória (recomenda-se Redis em produção)

### 3. Idempotência

- Previne processamento duplicado de pagamentos
- Armazena IDs processados no Firestore na coleção `processed_payments`

### 4. Logs de Segurança

- Registra tentativas de acesso inválidas
- Monitora rate limiting
- Logs detalhados para auditoria

## Formato dos Order Codes e Metadata

### Formato Atual

**Order Code Simples:**

```
PM_{packageId}_{timestamp_base36}
```

Exemplo: `PM_premium_k7x2m1`

**Informações no Metadata:**

```json
{
  "userId": "abc123def456ghi789",
  "packageId": "premium",
  "app": "postermuse",
  "version": "1.0"
}
```

### Vantagens do Formato

1. **Order Code Compacto**: Sempre < 20 caracteres
2. **Informações Estruturadas**: Metadata organizado e tipado
3. **Extensibilidade**: Fácil adicionar novos campos no metadata
4. **Performance**: Acesso direto às propriedades
5. **Confiabilidade**: Tipado e menos propenso a erros
6. **Conformidade**: Respeita limite de 52 caracteres do Pagar.me

## Estrutura da Coleção `processed_payments`

```typescript
{
  id: string, // chargeId (Pagar.me) ou paymentId (Mercado Pago)
  chargeId?: string, // Para Pagar.me
  paymentId?: string, // Para Mercado Pago
  orderCode: string, // Formato novo ou antigo
  processedAt: string, // ISO timestamp
  gateway: 'pagarme' | 'mercadopago'
}
```

## Monitoramento

### Logs Importantes

- `Rate limit exceeded for IP: {ip}` - Possível ataque
- `Invalid {gateway} webhook signature` - Tentativa de acesso não autorizada
- `Payment {id} already processed` - Tentativa de reprocessamento

### Métricas Recomendadas

- Taxa de webhooks rejeitados por assinatura inválida
- Número de tentativas de rate limiting
- Tempo de resposta dos webhooks

## Produção vs Desenvolvimento

### Desenvolvimento

- Validação de assinatura é opcional (com warning)
- Rate limiting mais permissivo
- Logs mais verbosos

### Produção

- Validação de assinatura obrigatória
- Rate limiting rigoroso
- Considere usar Redis para rate limiting
- Configure alertas para tentativas de acesso suspeitas

## Troubleshooting

### Webhook não está sendo processado

1. Verifique se o secret está configurado corretamente
2. Confirme se a URL do webhook está acessível
3. Verifique os logs para erros de assinatura

### Rate limiting muito restritivo

1. Ajuste `RATE_LIMIT_MAX_REQUESTS` se necessário
2. Considere whitelist para IPs dos gateways
3. Implemente Redis para rate limiting distribuído

### Pagamentos duplicados

1. Verifique se a coleção `processed_payments` está sendo criada
2. Confirme se o Firestore tem permissões adequadas
3. Monitore logs de idempotência
