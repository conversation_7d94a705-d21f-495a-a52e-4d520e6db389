# **App Name**: PosterMuse

## Core Features:

- Prompt Input: User-friendly interface for entering and editing text prompts.
- Prompt Enhancement: AI-powered prompt enhancement using Gemini API to refine user prompts for better poster generation outcomes. Gemini is used as a tool in prompt creation.
- Image-to-Prompt Analysis: Image analysis via Gemini API to generate a supplemental text prompt that captures key stylistic elements from an image uploaded by the user. Gemini is used as a tool in prompt creation.
- Poster Generation: Poster generation via Gemini API, taking the enhanced prompt, or a combination of the enhanced user prompt and image analysis prompt, and returns an image URL for the generated poster.
- Poster Display: Display of the generated poster in high resolution.
- Download Poster: Option to download the generated poster as a high-quality image file.

## Style Guidelines:

- Primary color: Deep Indigo (#4B0082) to convey creativity and sophistication.
- Background color: Light Lavender (#E6E6FA), offering a soft, desaturated contrast.
- Accent color: Golden Yellow (#FFD700) for highlights and call-to-action elements.
- Body text: 'Inter', a sans-serif font known for its readability and clean design.
- Headlines: '<PERSON><PERSON>' , a humanist sans-serif for headlines.
- Use minimalist and modern icons with a thin stroke to maintain a clean and elegant aesthetic.
- Maintain a balanced layout with clear visual hierarchy. Prioritize spaciousness and readability to ensure user-friendly navigation.