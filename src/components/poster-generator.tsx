"use client";

import * as React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import {
  Wand2,
  UploadCloud,
  ImageIcon,
  Loader2,
  History,
  X,
  Info,
  Gem,
} from 'lucide-react';
import { enhancePrompt } from '@/ai/flows/enhance-prompt';
import { generatePoster } from '@/ai/flows/generate-poster';
import { analyzeImage } from '@/ai/flows/analyze-image';
import { addPoster, getPosters, type Poster } from '@/services/posterService';
import { deductCoins, refundCoins } from '@/services/walletService';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { PosterCard } from './poster-card';
import { useAuth } from '@/hooks/useAuth';
import { Alert, AlertDescription, AlertTitle } from './ui/alert';
import { Icons } from './icons';
import { imageModels, type ImageModel } from '@/config/models';
import { appConfig } from '@/config/app';

type Status = 'idle' | 'analyzing' | 'generating' | 'uploading' | 'success' | 'error';

const formatOptions = [
  { label: 'Instagram Post (1080x1080)', value: '1:1' },
  { label: 'Instagram Story (1080x1920)', value: '9:16' },
  { label: 'YouTube Thumbnail (1280x720)', value: '16:9' },
  { label: 'Portrait Post (1080x1350)', value: '4:5' },
];

export default function PosterGenerator() {
  const [prompt, setPrompt] = React.useState('');
  const [aspectRatio, setAspectRatio] = React.useState(formatOptions[0].value);
  const [selectedModel, setSelectedModel] = React.useState<ImageModel>(imageModels[0]);
  const [imageFile, setImageFile] = React.useState<File | null>(null);
  const [imagePreviewUrl, setImagePreviewUrl] = React.useState<string | null>(null);
  const [imageDataUri, setImageDataUri] = React.useState<string | null>(null);
  const [imageAnalysisPrompt, setImageAnalysisPrompt] = React.useState<string | null>(null);
  const [generatedPosterUrl, setGeneratedPosterUrl] = React.useState<string | null>(null);
  const [recentPosters, setRecentPosters] = React.useState<Poster[]>([]);
  const [status, setStatus] = React.useState<Status>('idle');
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const { toast } = useToast();
  const searchParams = useSearchParams();
  const { user, profile, wallet, loading: authLoading, forceWalletRefetch } = useAuth();

  const isLoading = status === 'analyzing' || status === 'generating' || status === 'uploading';
  const generationCost = selectedModel.cost;

  const totalBalance = (wallet?.coins ?? 0) + (wallet?.dailyBonus?.coins ?? 0);
  const hasInsufficientFunds = !authLoading && user && totalBalance < generationCost;

  React.useEffect(() => {
    const remixPrompt = searchParams.get('prompt');
    if (remixPrompt) {
      setPrompt(decodeURIComponent(remixPrompt));
      window.scrollTo(0, 0);
    }
  }, [searchParams]);

  React.useEffect(() => {
    async function fetchRecentPosters() {
      try {
        const posters = await getPosters(4);
        setRecentPosters(posters);
      } catch (error) {
        console.error("Failed to fetch recent posters:", error);
        toast({
          title: 'Failed to load recent creations',
          description: 'Could not fetch recently created posters.',
          variant: 'destructive'
        })
      }
    }
    fetchRecentPosters();
  }, [toast]);


  const handleImageChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setStatus('analyzing');
      setImageFile(file);
      const previewUrl = URL.createObjectURL(file);
      setImagePreviewUrl(previewUrl);

      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onloadend = async () => {
        const dataUri = reader.result as string;
        setImageDataUri(dataUri);
        try {
          const result = await analyzeImage({ photoDataUri: dataUri });
          setImageAnalysisPrompt(result.prompt);
          setStatus('idle');
          toast({
            title: 'Image analysis complete',
            description: 'Your image has been analyzed and is ready to use.',
          });
        } catch (error) {
          console.error('Image analysis failed:', error);
          toast({
            title: 'Image Analysis Failed',
            description: 'Could not analyze the image. Please try another one.',
            variant: 'destructive',
          });
          resetImage();
          setStatus('error');
        }
      };
    }
  };

  const resetImage = () => {
    setImageFile(null);
    setImagePreviewUrl(null);
    setImageDataUri(null);
    setImageAnalysisPrompt(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    if (!user) {
      toast({ title: 'Acesso Negado', description: 'Você precisa estar logado para gerar pôsteres.', variant: 'destructive' });
      return;
    }

    if (!prompt) {
      toast({ title: 'Prompt é obrigatório', description: 'Por favor, insira um prompt para gerar um pôster.', variant: 'destructive' });
      return;
    }

    if (hasInsufficientFunds) {
      toast({ title: 'Créditos Insuficientes', description: `Você não tem MuseCoins suficientes. Custa ${generationCost} para gerar.`, variant: 'destructive' });
      return;
    }

    setStatus('generating');
    setGeneratedPosterUrl(null);
    let creditsDeducted = false;

    try {
      await deductCoins(user.uid, generationCost);
      creditsDeducted = true;
      forceWalletRefetch(); // Refresh wallet in UI

      let finalPrompt = prompt;
      if (prompt) {
        const { enhancedPrompt } = await enhancePrompt({ prompt });
        finalPrompt = enhancedPrompt;
      }

      if (imageAnalysisPrompt) {
        finalPrompt = `${finalPrompt}\\n\\nStyle reference from image: ${imageAnalysisPrompt}`;
      }

      const { posterUrl: posterDataUri } = await generatePoster({
        prompt: finalPrompt,
        styleGuidePrompt: profile?.styleGuidePrompt || undefined,
        image: imageDataUri ?? undefined,
        aspectRatio: aspectRatio,
        modelId: selectedModel.modelId,
      });

      setStatus('uploading');

      const newPoster = await addPoster({
        prompt,
        finalPrompt,
        posterDataUri,
        aspectRatio,
        userId: user.uid,
        styleGuidePrompt: profile?.styleGuidePrompt,
      });

      setGeneratedPosterUrl(newPoster.posterUrl);
      setRecentPosters(prev => [newPoster as Poster, ...prev].slice(0, 4));
      setStatus('success');
      toast({ title: 'Pôster Gerado!', description: `Foram debitados ${generationCost} MuseCoins.` });

    } catch (error) {
      console.error('Failed to generate poster:', error);
      toast({
        title: 'Falha na Geração',
        description: 'Algo deu errado ao criar seu pôster. Por favor, tente novamente.',
        variant: 'destructive',
      });
      setStatus('error');

      // Refund credits if they were deducted
      if (creditsDeducted) {
        await refundCoins(user.uid, generationCost);
        forceWalletRefetch(); // Refresh wallet in UI
        toast({ title: 'Créditos Estornados', description: `Seus ${generationCost} MuseCoins foram devolvidos.` });
      }
    }
  };

  const getStatusMessage = () => {
    switch (status) {
      case 'generating': return 'Gerando...';
      case 'uploading': return 'Salvando pôster...';
      default: return 'Gerar Pôster';
    }
  }

  return (
    <div className="container mx-auto px-4 py-12">
      <header className="text-center mb-12">
        <h1 className="font-headline text-5xl md:text-6xl font-bold tracking-tighter text-primary-dark">
          {appConfig.appName}
        </h1>
        <p className="text-muted-foreground mt-2 text-lg max-w-2xl mx-auto">
          Liberte sua criatividade. Descreva sua visão, opcionalmente adicione uma
          imagem de referência, e deixe nossa IA criar o pôster perfeito para você.
        </p>
      </header>

      <div className="grid grid-cols-1 lg:grid-cols-2 lg:gap-12 gap-8 items-start">
        <Card className="shadow-lg border-primary/20">
          <form onSubmit={handleSubmit}>
            <CardHeader>
              <CardTitle className="font-headline text-3xl">
                1. Descreva Sua Visão
              </CardTitle>
              <CardDescription>
                Comece com um prompt. O custo da geração depende do modelo de IA escolhido.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {!user && !authLoading && (
                <Alert variant="default" className="border-primary/50 bg-primary/10">
                  <Info className="h-4 w-4" />
                  <AlertTitle>Bem-vindo ao {appConfig.appName}!</AlertTitle>
                  <AlertDescription>
                    <Link href="/login" className="font-bold underline">Faça login</Link> ou <Link href="/signup" className="font-bold underline">cadastre-se</Link> para começar a criar e ganhar 100 MuseCoins grátis!
                  </AlertDescription>
                </Alert>
              )}
              {hasInsufficientFunds && (
                <Alert variant="destructive">
                  <Gem className="h-4 w-4" />
                  <AlertTitle>Créditos Insuficientes!</AlertTitle>
                  <AlertDescription>
                    Você não tem MuseCoins suficientes para gerar um pôster.{' '}
                    <Button variant="link" asChild className="p-0 h-auto ml-1 text-destructive-foreground underline">
                      <Link href="/buy-coins">Compre mais moedas.</Link>
                    </Button>
                  </AlertDescription>
                </Alert>
              )}
              <div className="space-y-2">
                <Label htmlFor="prompt" className="text-lg font-medium">
                  Prompt
                </Label>
                <Textarea
                  id="prompt"
                  placeholder="Ex: Uma paisagem surreal com ilhas flutuantes e uma lua gigante, no estilo de Salvador Dali."
                  rows={5}
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  className="text-base"
                  disabled={isLoading || !user}
                />
              </div>

              <div className="space-y-2">
                <Label className="text-lg font-medium">Modelo de IA</Label>
                <RadioGroup
                  value={selectedModel.id}
                  onValueChange={(modelId) => {
                    const model = imageModels.find((m) => m.id === modelId);
                    if (model) setSelectedModel(model);
                  }}
                  className="grid grid-cols-1 md:grid-cols-2 gap-4"
                  disabled={isLoading || !user}
                >
                  {imageModels.map((model) => (
                    <Label
                      key={model.id}
                      htmlFor={model.id}
                      className={cn(
                        'flex flex-col items-start gap-2 rounded-md border p-4 cursor-pointer hover:bg-muted/50 transition-colors',
                        selectedModel.id === model.id && 'border-primary ring-2 ring-primary'
                      )}
                    >
                      <div className="flex w-full items-center justify-between">
                        <div className="flex items-center gap-3">
                          <RadioGroupItem value={model.id} id={model.id} />
                          <span className="font-semibold text-base">{model.name}</span>
                        </div>
                        <div className="flex items-center gap-1 text-sm font-bold">
                          <span>{model.cost}</span> <Icons.Coins className="h-4 w-4 text-amber-400" />
                        </div>
                      </div>
                      <p className="text-xs text-muted-foreground pl-7">{model.description}</p>
                    </Label>
                  ))}
                </RadioGroup>
              </div>

              <div className="space-y-2">
                <Label htmlFor="format" className="text-lg font-medium">
                  Formato
                </Label>
                <Select
                  value={aspectRatio}
                  onValueChange={setAspectRatio}
                  disabled={isLoading || !user}
                >
                  <SelectTrigger id="format" className="w-full">
                    <SelectValue placeholder="Selecione um formato" />
                  </SelectTrigger>
                  <SelectContent>
                    {formatOptions.map((option) => (
                      <SelectItem key={option.label} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="image-upload" className="text-lg font-medium">
                  Imagem de Referência (Opcional)
                </Label>
                {imagePreviewUrl ? (
                  <div className="relative group aspect-video w-full rounded-lg overflow-hidden border">
                    <Image
                      src={imagePreviewUrl}
                      alt="Pré-visualização da imagem"
                      fill
                      sizes="50vw"
                      className={cn(
                        'object-cover transition-opacity',
                        status === 'analyzing' && 'opacity-50'
                      )}
                    />
                    {status === 'analyzing' && (
                      <div className="absolute inset-0 flex items-center justify-center bg-background/50 backdrop-blur-sm">
                        <Loader2 className="w-8 h-8 text-primary animate-spin" />
                        <span className="ml-2 font-medium">Analisando...</span>
                      </div>
                    )}
                    <button
                      type="button"
                      onClick={resetImage}
                      disabled={isLoading || !user}
                      className="absolute top-2 right-2 z-10 p-1.5 bg-card/70 text-card-foreground rounded-full backdrop-blur-sm transition-all opacity-0 group-hover:opacity-100 focus:opacity-100 disabled:opacity-50 disabled:cursor-not-allowed"
                      aria-label="Remover imagem"
                    >
                      <X className="w-5 h-5" />
                    </button>
                  </div>
                ) : (
                  <Label
                    htmlFor="image-upload"
                    className={cn(
                      "relative flex flex-col items-center justify-center w-full aspect-video border-2 border-dashed rounded-lg transition-colors",
                      user ? "cursor-pointer hover:bg-muted/50" : "cursor-not-allowed opacity-50"
                    )}
                  >
                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                      <UploadCloud className="w-10 h-10 mb-3 text-muted-foreground" />
                      <p className="mb-2 text-sm text-muted-foreground">
                        <span className="font-semibold text-primary">
                          Clique para enviar
                        </span>{' '}
                        ou arraste e solte
                      </p>
                      <p className="text-xs text-muted-foreground">
                        PNG, JPG, ou WEBP
                      </p>
                    </div>
                    <Input
                      id="image-upload"
                      type="file"
                      className="hidden"
                      accept="image/png, image/jpeg, image/webp"
                      onChange={handleImageChange}
                      ref={fileInputRef}
                      disabled={isLoading || !user}
                    />
                  </Label>
                )}
              </div>
            </CardContent>
            <CardFooter>
              <Button
                type="submit"
                size="lg"
                className="w-full text-lg font-bold bg-primary hover:bg-primary/90 text-primary-foreground"
                disabled={isLoading || !prompt || !user || (!!hasInsufficientFunds)}
              >
                {isLoading ? (
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                ) : (
                  <Wand2 className="mr-2 h-5 w-5" />
                )}
                <span>{getStatusMessage()}</span>
                {!isLoading && user && <span className="text-xs font-normal opacity-80">({generationCost} <Icons.Coins className="inline-block -mt-0.5" />)</span>}
              </Button>
            </CardFooter>
          </form>
        </Card>

        <div className="sticky top-8">
          <Card className="shadow-lg border-primary/20">
            <CardHeader>
              <CardTitle className="font-headline text-3xl">
                2. Sua Obra-Prima
              </CardTitle>
              <CardDescription>
                Seu pôster gerado aparecerá aqui.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="aspect-[3/4] w-full rounded-lg bg-muted/50 flex items-center justify-center overflow-hidden">
                {isLoading && (
                  <div className="w-full h-full flex flex-col items-center justify-center">
                    <Skeleton className="h-full w-full" />
                  </div>
                )}
                {!isLoading && !generatedPosterUrl && (
                  <div className="text-center text-muted-foreground p-8">
                    <ImageIcon className="mx-auto h-16 w-16 mb-4" />
                    <p className="font-medium">
                      Vamos criar algo incrível!
                    </p>
                  </div>
                )}
                {generatedPosterUrl && (
                  <Image
                    src={generatedPosterUrl}
                    alt={prompt || 'Pôster Gerado'}
                    width={600}
                    height={800}
                    className="h-full w-full object-contain"
                  />
                )}
              </div>
            </CardContent>
            {status === 'success' && generatedPosterUrl && (
              <CardFooter>
                <p className="text-sm text-muted-foreground w-full text-center">
                  Sua criação foi salva! Veja em <Link href="/discovery" className="underline hover:text-primary">Discovery</Link>.
                </p>
              </CardFooter>
            )}
          </Card>
        </div>
      </div>

      <div className="mt-24">
        <div className="flex items-center gap-4 mb-8">
          <History className="w-8 h-8 text-primary" />
          <h2 className="font-headline text-4xl font-bold tracking-tighter">
            Criações da Comunidade
          </h2>
        </div>
        {recentPosters.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {recentPosters.map((poster) => (
              <PosterCard key={poster.id} poster={poster} />
            ))}
          </div>
        ) : (
          <div className="text-center text-muted-foreground p-12 border-2 border-dashed rounded-lg">
            <p className="font-medium">Pôsteres recentes aparecerão aqui.</p>
            <p className="text-sm">Seja o primeiro a criar algo!</p>
          </div>
        )}
      </div>
    </div>
  );
}
