'use client';

import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { signOutUser } from '@/services/authService';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from './ui/button';
import { Icons } from './icons';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './ui/tooltip';
import Link from 'next/link';

export function UserNav() {
  const { user, wallet } = useAuth();
  const router = useRouter();

  if (!user) {
    return null;
  }

  const handleLogout = async () => {
    await signOutUser();
    router.push('/');
  };

  const getInitials = (email: string | null) => {
    if (!email) return '..';
    return email.substring(0, 2).toUpperCase();
  };
  
  const dailyBonusAvailable = wallet && wallet.dailyBonus && wallet.dailyBonus.coins > 0;

  return (
    <div className="flex items-center gap-4">
        <div className="flex items-center gap-4">
        <TooltipProvider>
            <Tooltip>
                <TooltipTrigger asChild>
                    <div className="flex items-center gap-1 text-sm font-medium text-amber-400">
                        <Icons.Coins className="h-4 w-4" />
                        <span>{wallet?.coins ?? '...'}</span>
                    </div>
                </TooltipTrigger>
                <TooltipContent>
                    <p>Seus MuseCoins</p>
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>

        {dailyBonusAvailable && (
             <TooltipProvider>
                <Tooltip>
                    <TooltipTrigger asChild>
                        <div className="flex items-center gap-1 text-sm font-medium text-cyan-400">
                            <Icons.Sparkles className="h-4 w-4" />
                            <span>{wallet.dailyBonus.coins}</span>
                        </div>
                    </TooltipTrigger>
                    <TooltipContent>
                        <p>Seus créditos bônus diários</p>
                    </TooltipContent>
                </Tooltip>
            </TooltipProvider>
        )}
      </div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="relative h-8 w-8 rounded-full">
            <Avatar className="h-8 w-8">
              <AvatarFallback>{getInitials(user.email)}</AvatarFallback>
            </Avatar>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56" align="end" forceMount>
          <DropdownMenuLabel className="font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">Minha Conta</p>
              <p className="text-xs leading-none text-muted-foreground">
                {user.email}
              </p>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem asChild>
                <Link href="/profile">Meu Perfil</Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
                <Link href="/buy-coins">Comprar MuseCoins</Link>
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleLogout}>
            Sair
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
