'use client';

import Image from 'next/image';
import Link from 'next/link';
import { Copy, Download, Shuffle } from 'lucide-react';
import type { Poster } from '@/services/posterService';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { appConfig } from '@/config/app';

interface PosterCardProps {
  poster: Poster;
}

export function PosterCard({ poster }: PosterCardProps) {
  const { toast } = useToast();

  const downloadImage = async (url: string, filename: string) => {
    if (!url) return;
    try {
      // Fetch the image as a blob
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('Network response was not ok.');
      }
      const blob = await response.blob();

      // Create a link and trigger the download
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);

    } catch (error) {
      console.error('Download failed:', error);
      // Fallback for CORS issues or other errors: open in new tab
      window.open(url, '_blank');
      toast({
        title: 'Download Failed',
        description: 'Could not download the poster directly. It has been opened in a new tab for you to save.',
        variant: 'destructive',
      });
    }
  };

  return (
    <Card key={poster.id} className="overflow-hidden group flex flex-col">
      <CardContent className="p-0 aspect-[3/4] relative">
        <Image
          src={poster.posterUrl}
          alt={poster.prompt}
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          className="object-cover transition-transform group-hover:scale-105"
        />
      </CardContent>
      <CardFooter className="p-4 flex-col items-start space-y-3 mt-auto bg-card">
        <ScrollArea className="h-20 w-full">
          <p className="text-sm text-muted-foreground">{poster.finalPrompt}</p>
        </ScrollArea>
        <div className="flex w-full gap-2 pt-2">
          <Button asChild variant="outline" size="sm" className="w-full">
            <Link href={`/?prompt=${encodeURIComponent(poster.finalPrompt)}`}>
              <Shuffle className="mr-2 h-4 w-4" />
              Remix
            </Link>
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="w-full"
            onClick={() => downloadImage(poster.posterUrl, `${appConfig.name}-${poster.id}.png`)}
          >
            <Download className="mr-2 h-4 w-4" />
            Baixar
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
