import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  type Auth,
  type UserCredential,
  type User,
} from 'firebase/auth';
import { auth } from '@/lib/firebase';
import { createUserWallet } from './walletService';

export type { User };

const FIREBASE_NOT_CONFIGURED_ERROR = "Firebase is not configured. Please check your environment variables.";

export async function signUp(email: string, password: string): Promise<UserCredential> {
  if (!auth) throw new Error(FIREBASE_NOT_CONFIGURED_ERROR);
  const userCredential = await createUserWithEmailAndPassword(auth, email, password);
  if (userCredential.user) {
    await createUserWallet(userCredential.user.uid);
  }
  return userCredential;
}

export async function signIn(email: string, password: string): Promise<UserCredential> {
  if (!auth) throw new Error(FIREBASE_NOT_CONFIGURED_ERROR);
  return signInWithEmailAndPassword(auth, email, password);
}

export async function signOutUser(): Promise<void> {
  if (!auth) throw new Error(FIREBASE_NOT_CONFIGURED_ERROR);
  return signOut(auth);
}
