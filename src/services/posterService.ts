import {
  collection,
  addDoc,
  getDocs,
  query,
  orderBy,
  limit,
  serverTimestamp,
  Timestamp,
  DocumentData,
} from 'firebase/firestore';
import { ref, uploadString, getDownloadURL } from 'firebase/storage';
import { firestore, storage } from '@/lib/firebase';

export interface Poster extends DocumentData {
  id: string;
  userId: string;
  prompt: string;
  finalPrompt: string;
  posterUrl: string;
  aspectRatio: string;
  styleGuidePrompt?: string;
  createdAt: Timestamp;
}

const FIREBASE_NOT_CONFIGURED_ERROR = "Firebase is not configured. Please check your environment variables.";

export async function addPoster(posterData: {
  prompt: string;
  finalPrompt: string;
  posterDataUri: string;
  aspectRatio: string;
  userId: string;
  styleGuidePrompt?: string;
}) {
  if (!firestore || !storage) {
    throw new Error(FIREBASE_NOT_CONFIGURED_ERROR);
  }

  const { prompt, finalPrompt, posterDataUri, aspectRatio, userId, styleGuidePrompt } = posterData;

  // 1. Upload image to Firebase Storage
  const storageRef = ref(storage, `posters/${Date.now()}-${Math.random().toString(36).substring(2)}.png`);
  const snapshot = await uploadString(storageRef, posterDataUri, 'data_url');
  const downloadURL = await getDownloadURL(snapshot.ref);

  // 2. Add poster metadata to Firestore
  const postersCollection = collection(firestore, 'posters');

  const dataToSave: Omit<Poster, 'id' | 'createdAt'> = {
    userId,
    prompt,
    finalPrompt,
    posterUrl: downloadURL,
    aspectRatio,
  };

  if (styleGuidePrompt) {
    dataToSave.styleGuidePrompt = styleGuidePrompt;
  }

  const docRef = await addDoc(postersCollection, {
    ...dataToSave,
    createdAt: serverTimestamp(),
  });

  return {
    id: docRef.id,
    ...dataToSave,
    createdAt: new Date(), // Using client-side date for immediate UI update
  };
}

export async function getPosters(count: number = 4): Promise<Poster[]> {
  if (!firestore) {
    console.warn("Firebase not configured. Returning empty recent posters array.");
    return [];
  }
  const postersCollection = collection(firestore, 'posters');
  const q = query(postersCollection, orderBy('createdAt', 'desc'), limit(count));
  const querySnapshot = await getDocs(q);
  return querySnapshot.docs.map(doc => {
    const data = doc.data();
    return {
      id: doc.id,
      createdAt: data.createdAt.toDate(),
      aspectRatio: data.aspectRatio,
      posterUrl: data.posterUrl,
      finalPrompt: data.finalPrompt,
      prompt: data.prompt,
      userId: data.userId,
      isPublic: data.isPublic,
    } as Poster;
  });
}

export async function getAllPosters(): Promise<Poster[]> {
  if (!firestore) {
    console.warn("Firebase not configured. Returning empty posters array for discovery page.");
    return [];
  }
  const postersCollection = collection(firestore, 'posters');
  const q = query(postersCollection, orderBy('createdAt', 'desc'));
  const querySnapshot = await getDocs(q);
  return querySnapshot.docs.map(doc => {
    const data = doc.data();
    return {
      id: doc.id,
      createdAt: data.createdAt.toDate(),
      aspectRatio: data.aspectRatio,
      posterUrl: data.posterUrl,
      finalPrompt: data.finalPrompt,
      prompt: data.prompt,
      userId: data.userId,
      isPublic: data.isPublic,
    } as Poster;
  });
}
