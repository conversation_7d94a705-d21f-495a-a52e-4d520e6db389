/**
 * Utilities for handling legacy order codes in webhooks
 * This is only for backward compatibility with old format order codes
 */

import { firestore } from '@/lib/firebase';
import { collection, query, where, getDocs, limit } from 'firebase/firestore';

export interface ParsedOrderCode {
  userId: string;
  packageId: string;
  timestamp: string;
  isValid: boolean;
  isShortUserId: boolean; // Indicates if userId is shortened
}

/**
 * Parses legacy order codes for backward compatibility
 * Old format: POSTERMUSE_{userId}__{packageId}__{timestamp}
 * New format: PM_{shortUserId}_{packageId}_{timestamp}
 */
export function parseOrderCode(orderCode: string): ParsedOrderCode {
  // Try new format first: PM_{shortUserId}_{packageId}_{timestamp}
  if (orderCode.startsWith('PM_')) {
    const parts = orderCode.split('_');
    if (parts.length >= 4) {
      const shortUserId = parts[1];
      const packageId = parts[2];
      const timestamp = parts[3];
      
      return {
        userId: shortUserId, // Note: this is the shortened userId
        packageId,
        timestamp,
        isValid: true,
        isShortUserId: true
      };
    }
  }
  
  // Try old format: POSTERMUSE_{userId}__{packageId}__{timestamp}
  if (orderCode.startsWith('POSTERMUSE_')) {
    const parts = orderCode.split('__');
    const userId = parts[0]?.replace('POSTERMUSE_', '');
    const packageId = parts[1];
    const timestamp = parts[2];
    
    if (userId && packageId) {
      return {
        userId,
        packageId,
        timestamp: timestamp || '',
        isValid: true,
        isShortUserId: false
      };
    }
  }
  
  // Invalid format
  return {
    userId: '',
    packageId: '',
    timestamp: '',
    isValid: false,
    isShortUserId: false
  };
}

/**
 * Finds the full userId from a shortened userId prefix
 * This is needed for legacy format order codes where userId is truncated
 */
export async function findFullUserId(shortUserId: string): Promise<string | null> {
  if (!firestore) {
    console.error('Firestore not configured');
    return null;
  }

  try {
    // Query users collection for documents where the document ID starts with shortUserId
    // Since Firestore doesn't support startsWith queries directly, we use range queries
    const usersRef = collection(firestore, 'users');
    const q = query(
      usersRef,
      where('__name__', '>=', shortUserId),
      where('__name__', '<', shortUserId + '\uf8ff'), // \uf8ff is a high Unicode character
      limit(1)
    );
    
    const querySnapshot = await getDocs(q);
    
    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      return doc.id;
    }
    
    return null;
  } catch (error) {
    console.error('Error finding full userId:', error);
    return null;
  }
}
