import axios from 'axios';
import {
  PagarmeOrderRequest,
  PagarmeOrderResponse,
  PagarmeItem,
  PagarmeCustomer,
  PagarmeCreditCardPayment,
} from './pagarmeInterfaces';

export class PagarmeService {
  private apiUrl: string;
  private secretKey: string;

  constructor(secretKey: string, apiUrl: string = 'https://api.pagar.me/core/v5') {
    this.secretKey = secretKey;
    this.apiUrl = apiUrl;
  }

  /**
   * Cria um novo pedido na Pagar.me.
   * @param orderData Dados do pedido a ser criado.
   * @returns Uma Promise que resolve para a resposta do pedido da Pagar.me.
   */
  public async createOrder(orderData: PagarmeOrderRequest): Promise<PagarmeOrderResponse> {
    try {
      const response = await axios.post<PagarmeOrderResponse>(
        `${this.apiUrl}/orders`,
        orderData,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Basic ${Buffer.from(this.secretKey + ':').toString('base64')}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('Erro ao criar pedido na Pagar.me:', error.response?.data || error.message);
        throw new Error(`Erro na API da Pagar.me: ${JSON.stringify(error.response?.data || error.message)}`);
      } else {
        console.error('Erro inesperado ao criar pedido:', error);
        throw new Error('Erro inesperado ao criar pedido.');
      }
    }
  }

  /**
   * Cria ou atualiza um customer na Pagar.me.
   * @param customer Dados do customer a ser criado ou atualizado.
   * @returns Uma Promise que resolve para a resposta do customer da Pagar.me.
   */
  public async createOrUpdateCustomer(customer: PagarmeCustomer): Promise<any> {
    try {
      const response = await axios.post<any>(
        `${this.apiUrl}/customers`,
        customer,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Basic ${btoa(this.secretKey + ':')}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('Erro ao criar/atualizar customer na Pagar.me:', error.response?.data || error.message);
        throw new Error(`Erro na API da Pagar.me: ${JSON.stringify(error.response?.data || error.message)}`);
      } else {
        console.error('Erro inesperado ao criar/atualizar customer:', error);
        throw new Error('Erro inesperado ao criar/atualizar customer.');
      }
    }
  }

  /**
   * Gera um objeto PagarmeItem para um pacote de moedas.
   * @param description Descrição do pacote.
   * @param amount Valor do pacote em reais (ex: 12.90).
   * @param quantity Quantidade de pacotes.
   * @returns Objeto PagarmeItem.
   */
  public static createCoinPackageItem(description: string, amount: number, quantity: number = 1): PagarmeItem {
    return {
      code: 'COIN_PACKAGE',
      amount: Math.round(amount * 100), // Converter para centavos
      description,
      quantity,
    };
  }

}
