/**
 * Tests for order code utilities
 */

import { generateOrderCode, parseOrderCode } from '../orderCodeUtils';

describe('Order Code Utils', () => {
  describe('generateOrderCode', () => {
    it('should generate compact order codes under 52 characters', () => {
      const userId = 'abcdefghijklmnopqrstuvwxyz123456789';
      const packageId = 'premium';
      
      const orderCode = generateOrderCode(userId, packageId);
      
      expect(orderCode.length).toBeLessThanOrEqual(52);
      expect(orderCode).toMatch(/^PM_[a-zA-Z0-9]+_premium_[a-z0-9]+$/);
    });

    it('should handle very long userIds gracefully', () => {
      const veryLongUserId = 'a'.repeat(100);
      const packageId = 'basic';
      
      const orderCode = generateOrderCode(veryLongUserId, packageId);
      
      expect(orderCode.length).toBeLessThanOrEqual(52);
      expect(orderCode).toMatch(/^PM_[a]+_basic_[a-z0-9]+$/);
    });
  });

  describe('parseOrderCode', () => {
    it('should parse new format order codes', () => {
      const orderCode = 'PM_abcdefgh_premium_abc123';
      
      const result = parseOrderCode(orderCode);
      
      expect(result.isValid).toBe(true);
      expect(result.userId).toBe('abcdefgh');
      expect(result.packageId).toBe('premium');
      expect(result.timestamp).toBe('abc123');
      expect(result.isShortUserId).toBe(true);
    });

    it('should parse old format order codes', () => {
      const orderCode = 'POSTERMUSE_fullUserId123__premium__1234567890';
      
      const result = parseOrderCode(orderCode);
      
      expect(result.isValid).toBe(true);
      expect(result.userId).toBe('fullUserId123');
      expect(result.packageId).toBe('premium');
      expect(result.timestamp).toBe('1234567890');
      expect(result.isShortUserId).toBe(false);
    });

    it('should handle invalid order codes', () => {
      const invalidCode = 'INVALID_FORMAT';
      
      const result = parseOrderCode(invalidCode);
      
      expect(result.isValid).toBe(false);
      expect(result.userId).toBe('');
      expect(result.packageId).toBe('');
      expect(result.timestamp).toBe('');
      expect(result.isShortUserId).toBe(false);
    });

    it('should handle old format without timestamp', () => {
      const orderCode = 'POSTERMUSE_userId123__basic';
      
      const result = parseOrderCode(orderCode);
      
      expect(result.isValid).toBe(true);
      expect(result.userId).toBe('userId123');
      expect(result.packageId).toBe('basic');
      expect(result.timestamp).toBe('');
      expect(result.isShortUserId).toBe(false);
    });
  });

  describe('integration', () => {
    it('should generate and parse order codes correctly', () => {
      const userId = 'testUser123456789';
      const packageId = 'premium';
      
      const generated = generateOrderCode(userId, packageId);
      const parsed = parseOrderCode(generated);
      
      expect(parsed.isValid).toBe(true);
      expect(parsed.packageId).toBe(packageId);
      expect(parsed.isShortUserId).toBe(true);
      expect(parsed.userId).toBe(userId.substring(0, 8)); // Should be shortened
    });
  });
});
