import { doc, getDoc, setDoc } from 'firebase/firestore';
import { auth, firestore } from '@/lib/firebase';
import { PagarmeService } from './pagarmeService';
import { PagarmeCustomer } from './pagarmeInterfaces';

const usersCollection = 'users';
const FIREBASE_NOT_CONFIGURED_ERROR =
  'Firebase is not configured. Please check your environment variables.';

export interface UserProfile {
  name: string;
  document: string; // CPF
  birthdate: string; // YYYY-MM-DD
  phone: {
    countryCode: string;
    areaCode: string;
    number: string;
  };
  address: {
    postalCode: string;
    street: string;
    number: string;
    complement?: string;
    neighborhood: string;
    city: string;
    state: string; // UF
    country: string; // BR
  };
  styleGuidePrompt?: string;
}

export async function getUserProfile(
  userId: string
): Promise<UserProfile | null> {
  if (!firestore) {
    console.warn('Firebase not configured. Cannot get user profile.');
    return null;
  }
  const userDocRef = doc(firestore, usersCollection, userId);
  const docSnap = await getDoc(userDocRef);

  if (docSnap.exists()) {
    const data = docSnap.data();
    return (data.profile as UserProfile) || null;
  }
  return null;
}

export async function saveUserProfile(
  userId: string,
  profileData: UserProfile
): Promise<void> {
  if (!firestore || !auth) throw new Error(FIREBASE_NOT_CONFIGURED_ERROR);

  const userEmail = auth.currentUser?.email;
  if (!userEmail) {
    throw new Error('User email not found.');
  }

  let pagarmeCustomerId: string | undefined = undefined;
  const secretKey = process.env.NEXT_PUBLIC_PAGARME_SECRET_KEY;

  if (secretKey) {
    try {
      const pagarmeService = new PagarmeService(secretKey);
      const pagarmeCustomer: PagarmeCustomer = {
        code: userId,
        name: profileData.name,
        email: userEmail,
        document: profileData.document,
        document_type: 'cpf',
        type: 'individual',
        birthdate: profileData.birthdate,
        address: {
          country: profileData.address.country,
          state: profileData.address.state,
          city: profileData.address.city,
          zip_code: profileData.address.postalCode.replace(/\D/g, ''),
          line_1: `${profileData.address.street}, ${profileData.address.number}`,
          line_2: profileData.address.complement || profileData.address.neighborhood,
        },
        phones: {
          mobile_phone: {
            country_code: profileData.phone.countryCode,
            area_code: profileData.phone.areaCode,
            number: profileData.phone.number,
          },
        },
      };

      const customerResponse = await pagarmeService.createOrUpdateCustomer(pagarmeCustomer);
      pagarmeCustomerId = customerResponse.id;

    } catch (error) {
      console.error("Failed to create/update Pagar.me customer:", error);
      // Decide if you want to stop the profile save or just log the error
      // For now, we'll log and continue, but you might want to throw here
      // throw new Error("Failed to sync profile with payment gateway.");
    }
  } else {
    console.warn("Pagar.me secret key not found. Skipping customer creation.");
  }


  const userDocRef = doc(firestore, usersCollection, userId);
  const dataToSave: { profile: UserProfile, pagarmeCustomerId?: string } = {
    profile: profileData,
  };

  if (pagarmeCustomerId) {
    dataToSave.pagarmeCustomerId = pagarmeCustomerId;
  }

  await setDoc(userDocRef, dataToSave, { merge: true });
}
