'use server';

import { MercadoPagoConfig, Payment } from 'mercadopago';
import type { PaymentCreateData } from 'mercadopago/dist/clients/payment/create/types';
import type { UserProfile } from './profileService';
import type { Package } from './packages';
import { appConfig } from '@/config/app';

async function createPixPayment(
  pkg: Package,
  profile: UserProfile,
  email: string,
  orderCode: string,
  userId: string,
  packageId: string
) {
  const accessToken = process.env.MERCADOPAGO_ACCESS_TOKEN;
  if (!accessToken) {
    throw new Error('Mercado Pago Access Token is not configured.');
  }
  const client = new MercadoPagoConfig({ accessToken });
  const paymentClient = new Payment(client);

  const expirationDate = new Date();
  expirationDate.setHours(expirationDate.getHours() + 1);

  const paymentRequest: PaymentCreateData = {
    body: {
      transaction_amount: pkg.priceInCents / 100,
      description: `${appConfig.appName} - ${pkg.name}`,
      payment_method_id: 'pix',
      payer: {
        email: email,
        first_name: profile.name.split(' ')[0],
        last_name: profile.name.split(' ').slice(1).join(' '),
        identification: {
          type: 'CPF',
          number: profile.document,
        },
        address: {
          zip_code: profile.address.postalCode,
          street_name: profile.address.street,
          street_number: profile.address.number,
          neighborhood: profile.address.neighborhood,
          city: profile.address.city,
          federal_unit: profile.address.state,
        },
      },
      external_reference: orderCode,
      notification_url: `${process.env.NEXT_PUBLIC_APP_URL}/api/webhooks/mercadopago`,
      date_of_expiration: expirationDate.toISOString(),
      metadata: {
        userId: userId,
        packageId: packageId,
        app: appConfig.name,
        version: appConfig.version,
      },
    },
  };

  try {
    const response = await paymentClient.create(paymentRequest);
    const qrCode = response.point_of_interaction?.transaction_data?.qr_code;
    const qrCodeBase64 =
      response.point_of_interaction?.transaction_data?.qr_code_base64;

    if (!qrCode || !qrCodeBase64) {
      throw new Error('QR Code data not found in Mercado Pago response.');
    }

    return {
      qrCodeUrl: `data:image/png;base64,${qrCodeBase64}`,
      qrCodeCopy: qrCode,
    };
  } catch (error) {
    console.error('Error creating Mercado Pago PIX payment:', error);
    const errorMessage =
      error instanceof Error ? JSON.stringify(error) : 'Unknown error';
    throw new Error(`Failed to create Mercado Pago payment: ${errorMessage}`);
  }
}

export { createPixPayment as createMercadoPagoPixPayment };
