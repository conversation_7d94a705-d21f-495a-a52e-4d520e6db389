import {
  doc,
  setDoc,
  getDoc,
  updateDoc,
  increment,
  runTransaction,
  serverTimestamp,
  Timestamp,
} from 'firebase/firestore';
import { firestore } from '@/lib/firebase';
import { isBefore, startOfToday } from 'date-fns';

export type Wallet = {
  id: string;
  coins: number;
  dailyBonus: {
    coins: number;
    lastUsed: Date;
  };
  createdAt: Date;
};



const usersCollection = 'users';
const FIREBASE_NOT_CONFIGURED_ERROR = "Firebase is not configured. Please check your environment variables.";

export async function createUserWallet(userId: string): Promise<void> {
  if (!firestore) throw new Error(FIREBASE_NOT_CONFIGURED_ERROR);
  const userWalletRef = doc(firestore, usersCollection, userId);
  const newWallet = {
    coins: 100, // Welcome bonus
    dailyBonus: {
      coins: 30,
      lastUsed: Timestamp.fromMillis(0), // Set to epoch to allow immediate use
    },
    createdAt: serverTimestamp(),
  };
  await setDoc(userWalletRef, newWallet);
}

export async function getUserWallet(userId: string): Promise<Wallet | null> {
  if (!firestore) {
    console.warn("Firebase not configured. Cannot get user wallet.");
    return null;
  }
  const userWalletRef = doc(firestore, usersCollection, userId);
  const walletDoc = await getDoc(userWalletRef);

  if (!walletDoc.exists()) {
    return null;
  }

  const firestoreData = walletDoc.data();

  // Convert Firestore data to client-safe format
  const walletData: Wallet = {
    id: walletDoc.id,
    coins: firestoreData.coins,
    dailyBonus: {
      coins: firestoreData.dailyBonus.coins,
      lastUsed: firestoreData.dailyBonus.lastUsed.toDate(),
    },
    createdAt: firestoreData.createdAt.toDate(),
  };

  // Check and reset daily bonus if needed
  const today = startOfToday();
  if (isBefore(walletData.dailyBonus.lastUsed, today)) {
    walletData.dailyBonus.coins = 30;
    // We don't update lastUsed here, only when a deduction happens.
  }

  return walletData;
}

export async function deductCoins(userId: string, amount: number): Promise<void> {
  if (!firestore) throw new Error(FIREBASE_NOT_CONFIGURED_ERROR);
  const userWalletRef = doc(firestore, usersCollection, userId);

  await runTransaction(firestore, async (transaction) => {
    const walletDoc = await transaction.get(userWalletRef);
    if (!walletDoc.exists()) {
      throw new Error("Carteira não encontrada.");
    }

    let firestoreWallet = walletDoc.data();

    // Check and reset daily bonus
    const today = startOfToday();
    if (isBefore(firestoreWallet.dailyBonus.lastUsed.toDate(), today)) {
      firestoreWallet.dailyBonus.coins = 30;
    }

    // Convert to client format for calculations
    const wallet = {
      coins: firestoreWallet.coins,
      dailyBonus: {
        coins: firestoreWallet.dailyBonus.coins,
        lastUsed: firestoreWallet.dailyBonus.lastUsed.toDate(),
      },
      createdAt: firestoreWallet.createdAt.toDate(),
    };

    const totalBalance = wallet.coins + wallet.dailyBonus.coins;
    if (totalBalance < amount) {
      throw new Error("Créditos insuficientes.");
    }

    let amountFromDaily = Math.min(amount, wallet.dailyBonus.coins);
    let newDailyCoins = wallet.dailyBonus.coins - amountFromDaily;

    let amountFromMain = amount - amountFromDaily;
    let newMainCoins = wallet.coins - amountFromMain;

    transaction.update(userWalletRef, {
      coins: newMainCoins,
      'dailyBonus.coins': newDailyCoins,
      'dailyBonus.lastUsed': serverTimestamp(),
    });
  });
}

export async function refundCoins(userId: string, amount: number): Promise<void> {
  if (!firestore) throw new Error(FIREBASE_NOT_CONFIGURED_ERROR);
  const userWalletRef = doc(firestore, usersCollection, userId);
  try {
    await updateDoc(userWalletRef, {
      coins: increment(amount),
    });
  } catch (error) {
    console.error("Failed to refund coins:", error);
    // Handle the case where the user document might not exist, though unlikely in this flow.
  }
}

export async function addCoins(userId: string, amount: number): Promise<void> {
  if (!firestore) throw new Error(FIREBASE_NOT_CONFIGURED_ERROR);
  const userWalletRef = doc(firestore, usersCollection, userId);
  try {
    await updateDoc(userWalletRef, {
      coins: increment(amount),
    });
  } catch (error) {
    console.error("Failed to add coins:", error);
    throw new Error('Não foi possível adicionar moedas à carteira.');
  }
}
