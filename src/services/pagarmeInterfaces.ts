/**
 * @fileOverview Interface definitions for Pagar.me API integration.
 * These types represent the structure of requests and responses
 * when communicating with the Pagar.me service.
 */

export interface PagarmeItem {
  code: string;
  amount: number; // Value in cents
  description: string;
  quantity: number;
}

export interface PagarmeAddress {
  country: string;
  state: string;
  city: string;
  zip_code: string;
  line_1: string;
  line_2?: string;
}

export interface PagarmeCustomer {
  code?: string;
  name: string;
  email: string;
  type: 'individual' | 'company';
  document: string;
  document_type: 'cpf' | 'cnpj';
  birthdate?: string;
  address: PagarmeAddress;
  phones?: {
    mobile_phone?: {
      country_code: string;
      area_code: string;
      number: string;
    };
  };
}

export interface PagarmeCreditCardPayment {
  payment_method: 'credit_card';
  credit_card: {
    installments: number;
    statement_descriptor?: string;
    // For PCI Compliance, use card_id obtained from a secure client-side integration.
    // Do not handle raw card data on the server.
    card_id?: string;
  };
}

export interface PagarmePixPayment {
  payment_method: 'pix';
  pix: {
    expires_in: number; // Expiration time in seconds
    additional_information?: {
        name: string;
        value: string;
      }[];
  };
}

export type PagarmePayment = PagarmeCreditCardPayment | PagarmePixPayment;

export interface PagarmeOrderRequest {
  code: string; // A unique identifier for the order in your system
  customer_id?: string;
  customer?: PagarmeCustomer;
  items: PagarmeItem[];
  payments: PagarmePayment[];
  closed?: boolean;
  metadata?: { [key: string]: string };
}

// Interfaces for Response objects

export interface PagarmePixTransaction {
  qr_code: string;
  qr_code_url: string; // This is a base64 encoded PNG
  expires_at: string;
  // ... other pix transaction fields
}

export interface PagarmeCharge {
  id: string;
  code: string;
  amount: number;
  status: 'pending' | 'paid' | 'canceled' | 'failed';
  payment_method: 'pix' | 'credit_card';
  last_transaction: PagarmePixTransaction; // Simplified for PIX for now
  order: {
    id: string;
    code: string;
  };
  // ... other charge fields can be added here
}

export interface PagarmeOrderResponse {
  id: string;
  code: string;
  amount: number;
  currency: string;
  closed: boolean;
  items: PagarmeItem[];
  customer: PagarmeCustomer;
  status: 'pending' | 'paid' | 'canceled' | 'failed';
  created_at: string;
  updated_at: string;
  charges: PagarmeCharge[];
}
