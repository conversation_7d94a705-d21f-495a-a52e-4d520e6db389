'use server';

import { PagarmeService } from './pagarmeService';
import { createMercadoPagoPixPayment as createMercadoPagoPixPaymentInternal } from './mercadopagoService';
import { PagarmeOrderRequest } from './pagarmeInterfaces';
import { firestore } from '@/lib/firebase';
import { doc, getDoc } from 'firebase/firestore';
import { packages } from './packages';
import type { UserProfile } from './profileService';

/**
 * Generates a simple, short order code
 * Format: PM_{timestamp_base36}
 */
function generateSimpleOrderCode(): string {
  const timestamp = Date.now().toString(36);
  return `PM_${timestamp}`;
}


type CreateOrderResult = {
  success: boolean;
  qrCodeUrl?: string;
  qrCodeCopy?: string;
  error?: string;
};


export async function createPixOrder(
  userId: string,
  packageId: string,
  userProfile: UserProfile,
  userEmail: string,
  pagarmeCustomerId?: string,
): Promise<CreateOrderResult> {
  const pixGateway = process.env.PAYMENT_GATEWAY_PIX || 'PAGARME'; // Default to Pagarme if not set

  const pkg = packages.find((p) => p.id === packageId);
  if (!pkg) {
    return { success: false, error: 'Pacote não encontrado.' };
  }

  if (!userProfile) {
    return { success: false, error: 'Seu perfil de pagamento está incompleto. Por favor, preencha seus dados.' };
  }

  const orderCode = generateSimpleOrderCode();

  if (pixGateway.toUpperCase() === 'MERCADOPAGO') {
    return createMercadoPagoPixOrder(pkg, userProfile, userEmail, orderCode, userId, packageId);
  } else {
    // Default to Pagar.me
    if (!pagarmeCustomerId) {
      return { success: false, error: 'ID do cliente Pagar.me não encontrado. Salve seu perfil para criar um.' };
    }
    return createPagarmePixOrder(pkg, orderCode, pagarmeCustomerId, userId, packageId);
  }
}

async function createPagarmePixOrder(
  pkg: (typeof packages)[0],
  orderCode: string,
  pagarmeCustomerId: string,
  userId: string,
  packageId: string
): Promise<CreateOrderResult> {
  const secretKey = process.env.NEXT_PUBLIC_PAGARME_SECRET_KEY;
  if (!secretKey) {
    console.error('Pagar.me secret key is not configured.');
    return { success: false, error: 'O sistema de pagamento (Pagar.me) não está configurado corretamente.' };
  }

  const pagarmeService = new PagarmeService(secretKey);

  const orderRequest: PagarmeOrderRequest = {
    code: orderCode,
    customer_id: pagarmeCustomerId,
    items: [{
      code: pkg.id,
      amount: pkg.priceInCents,
      description: `Pacote de Moedas: ${pkg.name}`,
      quantity: 1,
    }],
    payments: [{
      payment_method: 'pix',
      pix: {
        expires_in: 3600, // 1 hour
      }
    }],
    metadata: {
      userId: userId,
      packageId: packageId,
      app: 'postermuse',
      version: '1.0'
    }
  };

  try {
    const orderResponse = await pagarmeService.createOrder(orderRequest);
    const pixTransaction = orderResponse.charges[0]?.last_transaction;

    if (pixTransaction && pixTransaction.qr_code_url && pixTransaction.qr_code) {
      return {
        success: true,
        qrCodeUrl: pixTransaction.qr_code_url,
        qrCodeCopy: pixTransaction.qr_code,
      };
    } else {
      throw new Error('Resposta do PIX inválida do gateway de pagamento.');
    }
  } catch (error) {
    console.error('Failed to create Pagar.me order:', error);
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido.';
    return { success: false, error: `Falha ao comunicar com o gateway de pagamento (Pagar.me): ${errorMessage}` };
  }
}


async function createMercadoPagoPixOrder(
  pkg: (typeof packages)[0],
  profile: UserProfile,
  email: string,
  orderCode: string,
  userId: string,
  packageId: string
): Promise<CreateOrderResult> {
  const accessToken = process.env.MERCADOPAGO_ACCESS_TOKEN;
  if (!accessToken) {
    console.error("Mercado Pago access token is not configured.");
    return { success: false, error: "O sistema de pagamento (Mercado Pago) não está configurado corretamente." };
  }

  try {
    const pixData = await createMercadoPagoPixPaymentInternal(pkg, profile, email, orderCode, userId, packageId);
    return {
      success: true,
      ...pixData,
    };
  } catch (error) {
    console.error('Failed to create Mercado Pago PIX order:', error);
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido.';
    return { success: false, error: `Falha ao comunicar com o gateway de pagamento (Mercado Pago): ${errorMessage}` };
  }
}
