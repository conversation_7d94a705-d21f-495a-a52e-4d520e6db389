'use client';

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from 'react';
import { onAuthStateChanged, User } from 'firebase/auth';
import { auth, firestore } from '@/lib/firebase';
import { Wallet } from '@/services/walletService';
import { UserProfile } from '@/services/profileService';
import { doc, onSnapshot, DocumentData, getDoc } from 'firebase/firestore';

interface AuthContextType {
  user: User | null;
  wallet: Wallet | null;
  profile: UserProfile | null;
  pagarmeCustomerId: string | null;
  loading: boolean;
  forceWalletRefetch: () => void;
  forceProfileRefetch: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<User | null>(null);
  const [wallet, setWallet] = useState<Wallet | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [pagarmeCustomerId, setPagarmeCustomerId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  const refetchUserData = useCallback(async (uid: string) => {
    if (!firestore) return;
    try {
      const userRef = doc(firestore, 'users', uid);
      const docSnap = await getDoc(userRef);
      if (docSnap.exists()) {
        const data = docSnap.data() as DocumentData;
        const { profile: userProfile, pagarmeCustomerId: customerId, ...firestoreWalletData } = data;

        // Convert Firestore data to client-safe format
        const walletData: Wallet = {
          id: docSnap.id,
          coins: firestoreWalletData.coins || 0,
          dailyBonus: {
            coins: firestoreWalletData.dailyBonus?.coins || 0,
            lastUsed: firestoreWalletData.dailyBonus?.lastUsed?.toDate() || new Date(0),
          },
          createdAt: firestoreWalletData.createdAt?.toDate() || new Date(),
        };
        setWallet(walletData);
        setProfile((userProfile as UserProfile) || null);
        setPagarmeCustomerId(customerId || null);
      }
    } catch (error) {
      console.error('Failed to refetch user data:', error);
    }
  }, []);

  const forceWalletRefetch = useCallback(() => {
    if (user) {
      refetchUserData(user.uid);
    }
  }, [user, refetchUserData]);

  const forceProfileRefetch = useCallback(() => {
    if (user) {
      refetchUserData(user.uid);
    }
  }, [user, refetchUserData]);

  useEffect(() => {
    if (!auth || !firestore) {
      setLoading(false);
      return;
    }

    const unsubscribeAuth = onAuthStateChanged(auth, (currentUser) => {
      setUser(currentUser);
      if (currentUser) {
        const userDocRef = doc(firestore!, 'users', currentUser.uid);
        const unsubscribeUserDoc = onSnapshot(
          userDocRef,
          (doc) => {
            if (doc.exists()) {
              const data = doc.data() as DocumentData;
              const { profile: userProfile, pagarmeCustomerId: customerId, ...firestoreWalletData } = data;

              // Convert Firestore data to client-safe format
              const walletData: Wallet = {
                id: doc.id,
                coins: firestoreWalletData.coins || 0,
                dailyBonus: {
                  coins: firestoreWalletData.dailyBonus?.coins || 0,
                  lastUsed: firestoreWalletData.dailyBonus?.lastUsed?.toDate() || new Date(0),
                },
                createdAt: firestoreWalletData.createdAt?.toDate() || new Date(),
              };
              setWallet(walletData);
              setProfile((userProfile as UserProfile) || null);
              setPagarmeCustomerId(customerId || null);
            } else {
              console.log('No such user document!');
              setWallet(null);
              setProfile(null);
              setPagarmeCustomerId(null);
            }
            setLoading(false);
          },
          (error) => {
            console.error('User document listener error:', error);
            setLoading(false);
          }
        );

        return () => unsubscribeUserDoc();
      } else {
        setWallet(null);
        setProfile(null);
        setPagarmeCustomerId(null);
        setLoading(false);
      }
    });

    return () => unsubscribeAuth();
  }, []);

  const value = {
    user,
    wallet,
    profile,
    pagarmeCustomerId,
    loading,
    forceWalletRefetch,
    forceProfileRefetch,
  };

  return React.createElement(AuthContext.Provider, { value }, children);
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
