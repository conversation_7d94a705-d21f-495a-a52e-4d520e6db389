import { NextRequest, NextResponse } from 'next/server';
import { PagarmeService } from '@/services/pagarmeService';
import { PagarmeCustomer } from '@/services/pagarmeInterfaces';

export async function POST(request: NextRequest) {
  try {
    const secretKey = process.env.NEXT_PUBLIC_PAGARME_SECRET_KEY;
    if (!secretKey) {
      return NextResponse.json(
        { error: 'Pagar.me secret key not configured' },
        { status: 500 }
      );
    }

    const customerData: PagarmeCustomer = await request.json();
    
    const pagarmeService = new PagarmeService(secretKey);
    const customerResponse = await pagarmeService.createOrUpdateCustomer(customerData);
    
    return NextResponse.json(customerResponse);
  } catch (error) {
    console.error('Error creating/updating Pagar.me customer:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      { error: 'Failed to create/update customer', details: errorMessage },
      { status: 500 }
    );
  }
}
