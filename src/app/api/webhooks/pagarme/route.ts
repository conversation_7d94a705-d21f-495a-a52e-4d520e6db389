// src/app/api/webhooks/pagarme/route.ts
import { NextResponse } from 'next/server';
import { addCoins } from '@/services/walletService';
import { packages } from '@/services/packages';
import type { PagarmeCharge } from '@/services/pagarmeInterfaces';
import { firestore } from '@/lib/firebase';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import crypto from 'crypto';
import { parseOrderCode, findFullUserId } from '@/services/legacyOrderCodeUtils';

const PAGARME_WEBHOOK_SECRET = process.env.PAGARME_WEBHOOK_SECRET;

// Rate limiting - simple in-memory store (use Redis in production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 100; // max requests per window

function checkRateLimit(ip: string): boolean {
  const now = Date.now();
  const key = `webhook_${ip}`;
  const current = rateLimitStore.get(key);

  if (!current || now > current.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return true;
  }

  if (current.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false;
  }

  current.count++;
  return true;
}

function validatePagarmeSignature(body: string, signature: string | null): boolean {
  if (!PAGARME_WEBHOOK_SECRET || !signature) {
    console.warn('Pagar.me webhook signature validation skipped - secret or signature missing');
    return true; // Allow for development, but log warning
  }

  try {
    const expectedSignature = crypto
      .createHmac('sha256', PAGARME_WEBHOOK_SECRET)
      .update(body, 'utf8')
      .digest('hex');

    const providedSignature = signature.replace('sha256=', '');
    return crypto.timingSafeEqual(
      Buffer.from(expectedSignature, 'hex'),
      Buffer.from(providedSignature, 'hex')
    );
  } catch (error) {
    console.error('Error validating Pagar.me signature:', error);
    return false;
  }
}

async function isPaymentProcessed(chargeId: string): Promise<boolean> {
  if (!firestore) return false;

  try {
    const docRef = doc(firestore, 'processed_payments', chargeId);
    const docSnap = await getDoc(docRef);
    return docSnap.exists();
  } catch (error) {
    console.error('Error checking payment status:', error);
    return false;
  }
}

async function markPaymentAsProcessed(chargeId: string, orderCode: string): Promise<void> {
  if (!firestore) return;

  try {
    const docRef = doc(firestore, 'processed_payments', chargeId);
    await setDoc(docRef, {
      chargeId,
      orderCode,
      processedAt: new Date().toISOString(),
      gateway: 'pagarme'
    });
  } catch (error) {
    console.error('Error marking payment as processed:', error);
  }
}

export async function POST(request: Request) {
  try {
    // Rate limiting
    const clientIP = request.headers.get('x-forwarded-for') ||
      request.headers.get('x-real-ip') ||
      'unknown';

    if (!checkRateLimit(clientIP)) {
      console.warn(`Rate limit exceeded for IP: ${clientIP}`);
      return NextResponse.json({ error: 'Rate limit exceeded' }, { status: 429 });
    }

    // Get raw body for signature validation
    const rawBody = await request.text();
    const body = JSON.parse(rawBody);

    console.log('Pagar.me Webhook Received:', JSON.stringify(body, null, 2));

    // Signature validation
    const signature = request.headers.get('x-hub-signature-256') ||
      request.headers.get('x-hub-signature');

    if (!validatePagarmeSignature(rawBody, signature)) {
      console.error('Invalid Pagar.me webhook signature');
      return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
    }

    const { type, data } = body;

    if (type === 'charge.paid' && data.status === 'paid') {
      const charge = data as PagarmeCharge;
      const chargeId = charge.id;
      const orderCode = charge.order?.code;

      if (!orderCode || !chargeId) {
        console.error('Webhook Error: Order code or charge ID not found in charge object.');
        return NextResponse.json({ error: 'Order code or charge ID missing' }, { status: 400 });
      }

      // Check if payment was already processed (idempotency)
      if (await isPaymentProcessed(chargeId)) {
        console.log(`Payment ${chargeId} already processed, skipping.`);
        return NextResponse.json({ received: true, message: 'Already processed' }, { status: 200 });
      }

      // Try to get userId and packageId from metadata first (new format)
      const metadata = charge.order?.metadata;
      let userId = metadata?.userId;
      let packageId = metadata?.packageId;

      // Fallback to parsing order code (old format compatibility)
      if (!userId || !packageId) {
        console.log('Metadata not found, falling back to order code parsing');
        const parsed = parseOrderCode(orderCode);

        if (!parsed.isValid || !parsed.userId || !parsed.packageId) {
          console.error(`Webhook Error: Invalid order code format and no metadata: ${orderCode}`);
          return NextResponse.json({ error: 'Invalid order format' }, { status: 400 });
        }

        userId = parsed.userId;
        packageId = parsed.packageId;

        // If userId is shortened, find the full userId
        if (parsed.isShortUserId) {
          const fullUserId = await findFullUserId(userId);
          if (!fullUserId) {
            console.error(`Webhook Error: Could not find full userId for prefix: ${userId}`);
            return NextResponse.json({ error: 'User not found' }, { status: 404 });
          }
          userId = fullUserId;
        }
      }

      const pkg = packages.find((p) => p.id === packageId);
      if (!pkg) {
        console.error(`Webhook Error: Package not found for id: ${packageId}`);
        return NextResponse.json({ error: 'Package not found' }, { status: 404 });
      }

      // Process payment
      await addCoins(userId, pkg.coins);

      // Mark as processed to prevent duplicate processing
      await markPaymentAsProcessed(chargeId, orderCode);

      console.log(`Successfully added ${pkg.coins} coins to user ${userId} for charge ${chargeId}.`);
    }

    return NextResponse.json({ received: true }, { status: 200 });

  } catch (error) {
    console.error('Webhook processing error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ error: 'Internal Server Error', details: errorMessage }, { status: 500 });
  }
}
