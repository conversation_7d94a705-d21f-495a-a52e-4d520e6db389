// src/app/api/webhooks/pagarme/route.ts
import { NextResponse } from 'next/server';
import { addCoins } from '@/services/walletService';
import { packages } from '@/services/packages';
import type { PagarmeCharge } from '@/services/pagarmeInterfaces';

// In a real app, you'd use this to validate the webhook signature for security
// const PAGARME_WEBHOOK_SECRET = process.env.PAGARME_WEBHOOK_SECRET;

export async function POST(request: Request) {
  try {
    const body = await request.json();
    console.log('Pagar.me Webhook Received:', JSON.stringify(body, null, 2));

    // TODO: Implement Pagar.me signature validation for security
    // const signature = request.headers.get('x-hub-signature');
    // if (!isValidSignature(signature, body)) {
    //   return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
    // }

    const { type, data } = body;

    if (type === 'charge.paid' && data.status === 'paid') {
      const charge = data as PagarmeCharge;
      const orderCode = charge.order?.code;

      if (!orderCode) {
        console.error('Webhook Error: Order code not found in charge object.');
        return NextResponse.json({ error: 'Order code missing' }, { status: 400 });
      }

      // Expected format: `POSTERMUSE_${userId}__${packageId}`
      const parts = orderCode.split('__');
      const userId = parts[0]?.replace('POSTERMUSE_', '');
      const packageId = parts[1];


      if (!packageId || !userId) {
        console.error(`Webhook Error: Invalid order code format: ${orderCode}`);
        return NextResponse.json({ error: 'Invalid order code format' }, { status: 400 });
      }

      const pkg = packages.find((p) => p.id === packageId);
      if (!pkg) {
        console.error(`Webhook Error: Package not found for id: ${packageId}`);
        return NextResponse.json({ error: 'Package not found' }, { status: 404 });
      }

      await addCoins(userId, pkg.coins);
      console.log(`Successfully added ${pkg.coins} coins to user ${userId}.`);
    }

    return NextResponse.json({ received: true }, { status: 200 });

  } catch (error) {
    console.error('Webhook processing error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ error: 'Internal Server Error', details: errorMessage }, { status: 500 });
  }
}
