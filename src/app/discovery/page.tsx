import { getAllPosters } from '@/services/posterService';
import { PosterCard } from '@/components/poster-card';

export const revalidate = 60; // Revalidate every 60 seconds

export default async function DiscoveryPage() {
  const posters = await getAllPosters();

  return (
    <div className="container mx-auto px-4 py-12">
      <header className="text-center mb-12">
        <h1 className="font-headline text-5xl md:text-6xl font-bold tracking-tighter text-primary-dark">
          Discovery
        </h1>
        <p className="text-muted-foreground mt-2 text-lg max-w-2xl mx-auto">
          Explore all the amazing posters created by the PosterMuse community.
        </p>
      </header>

      {posters.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {posters.map((poster) => (
            <PosterCard key={poster.id} poster={poster} />
          ))}
        </div>
      ) : (
        <div className="text-center text-muted-foreground p-12 border-2 border-dashed rounded-lg">
          <p className="font-medium">No posters have been created yet.</p>
          <p className="text-sm">Be the first to create one!</p>
        </div>
      )}
    </div>
  );
}
