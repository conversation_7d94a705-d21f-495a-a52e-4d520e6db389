import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export default function Loading() {
  return (
    <main className="container mx-auto px-4 py-12">
      <header className="text-center mb-12">
        <Skeleton className="h-12 w-1/2 mx-auto" />
        <Skeleton className="h-6 w-3/4 mx-auto mt-4" />
      </header>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {Array.from({ length: 8 }).map((_, i) => (
          <Card key={i} className="overflow-hidden group flex flex-col">
            <CardContent className="p-0 aspect-[3/4] relative">
              <Skeleton className="h-full w-full" />
            </CardContent>
            <CardFooter className="p-4 flex-col items-start space-y-3 mt-auto bg-card">
              <Skeleton className="h-16 w-full" />
              <div className="flex w-full gap-2 pt-2">
                <Skeleton className="h-9 w-full" />
                <Skeleton className="h-9 w-full" />
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>
    </main>
  );
}
