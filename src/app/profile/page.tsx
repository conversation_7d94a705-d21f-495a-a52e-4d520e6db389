'use client';

import * as React from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useAuth } from '@/hooks/useAuth';
import { saveUserProfile, UserProfile } from '@/services/profileService';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';
import ProfilePageLoading from './loading';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';

const profileSchema = z.object({
  name: z.string().min(3, 'O nome completo é obrigatório.'),
  document: z
    .string()
    .regex(/^\d{11}$/, 'CPF inválido. Digite apenas os números.'),
  birthdate: z
    .string()
    .min(1, { message: 'A data de nascimento é obrigatória.' }),
  phone: z.object({
    areaCode: z.string().regex(/^\d{2}$/, 'DDD inválido.'),
    number: z.string().regex(/^\d{8,9}$/, 'Número inválido.'),
  }),
  address: z.object({
    postalCode: z
      .string()
      .regex(/^\d{5}-\d{3}$/, 'CEP inválido. Use o formato 00000-000.'),
    street: z.string().min(1, 'A rua é obrigatória.'),
    number: z.string().min(1, 'O número é obrigatório.'),
    complement: z.string().optional(),
    neighborhood: z.string().min(1, 'O bairro é obrigatório.'),
    city: z.string().min(1, 'A cidade é obrigatória.'),
    state: z.string().length(2, 'UF inválido. Use 2 letras (ex: SP).'),
  }),
  styleGuidePrompt: z.string().optional(),
});

type ProfileFormValues = z.infer<typeof profileSchema>;

export default function ProfilePage() {
  const router = useRouter();
  const { user, profile, loading, forceProfileRefetch } = useAuth();
  const { toast } = useToast();
  const [isSaving, setIsSaving] = React.useState(false);

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: '',
      document: '',
      birthdate: '',
      phone: { areaCode: '', number: '' },
      address: {
        postalCode: '',
        street: '',
        number: '',
        complement: '',
        neighborhood: '',
        city: '',
        state: '',
      },
      styleGuidePrompt: '',
    },
  });

  React.useEffect(() => {
    if (profile) {
      form.reset({
        name: profile.name || '',
        document: profile.document || '',
        birthdate: profile.birthdate || '',
        phone: {
          areaCode: profile.phone?.areaCode || '',
          number: profile.phone?.number || '',
        },
        address: {
          postalCode: profile.address?.postalCode || '',
          street: profile.address?.street || '',
          number: profile.address?.number || '',
          complement: profile.address?.complement || '',
          neighborhood: profile.address?.neighborhood || '',
          city: profile.address?.city || '',
          state: profile.address?.state || '',
        },
        styleGuidePrompt: profile.styleGuidePrompt || '',
      });
    }
  }, [profile, form]);

  async function onSubmit(values: ProfileFormValues) {
    if (!user) return;
    setIsSaving(true);
    try {
      const profileData: UserProfile = {
        ...values,
        phone: { ...values.phone, countryCode: '55' },
        address: { ...values.address, country: 'BR' },
      };
      await saveUserProfile(user.uid, profileData);
      forceProfileRefetch();
      toast({
        title: 'Perfil salvo com sucesso!',
        description: 'Suas informações foram atualizadas.',
      });
    } catch (error) {
      console.error(error);
      toast({
        title: 'Erro ao Salvar',
        description:
          'Não foi possível salvar suas informações. Tente novamente.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  }

  if (loading) {
    return <ProfilePageLoading />;
  }

  if (!user) {
    router.replace('/login');
    return null;
  }

  return (
    <div className="container mx-auto max-w-4xl px-4 py-12">
      <header className="mb-8">
        <h1 className="font-headline text-4xl font-bold tracking-tighter">
          Meu Perfil
        </h1>
        <p className="text-muted-foreground mt-2 text-lg">
          Mantenha seus dados atualizados para facilitar suas compras e personalize sua experiência.
        </p>
      </header>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Informações de Pagamento</CardTitle>
              <CardDescription>
                Esses dados serão usados para gerar os pedidos no nosso gateway
                de pagamento.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-8">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Dados Pessoais</h3>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nome Completo</FormLabel>
                        <FormControl>
                          <Input {...field} disabled={isSaving} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="document"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>CPF</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="00000000000"
                            {...field}
                            disabled={isSaving}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="birthdate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Data de Nascimento</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} disabled={isSaving} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input value={user?.email ?? ''} disabled />
                  </FormControl>
                </FormItem>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Telefone Celular</h3>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <div className="md:col-span-1">
                    <FormField
                      control={form.control}
                      name="phone.areaCode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>DDD</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="11"
                              {...field}
                              disabled={isSaving}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="md:col-span-2">
                    <FormField
                      control={form.control}
                      name="phone.number"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Número</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="999999999"
                              {...field}
                              disabled={isSaving}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Endereço</h3>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="address.postalCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>CEP</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="00000-000"
                            {...field}
                            disabled={isSaving}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <FormField
                  control={form.control}
                  name="address.street"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Rua / Logradouro</FormLabel>
                      <FormControl>
                        <Input {...field} disabled={isSaving} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="address.number"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Número</FormLabel>
                        <FormControl>
                          <Input {...field} disabled={isSaving} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="md:col-span-2">
                    <FormField
                      control={form.control}
                      name="address.complement"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Complemento (opcional)</FormLabel>
                          <FormControl>
                            <Input {...field} disabled={isSaving} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="address.neighborhood"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bairro</FormLabel>
                        <FormControl>
                          <Input {...field} disabled={isSaving} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="address.city"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Cidade</FormLabel>
                        <FormControl>
                          <Input {...field} disabled={isSaving} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="address.state"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Estado (UF)</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="SP"
                            {...field}
                            disabled={isSaving}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Guia de Estilo</CardTitle>
              <CardDescription>
                Defina um prompt padrão para ser enviado em todas as suas
                gerações. Use para manter uma identidade visual, definir estilos,
                cores ou o idioma das artes.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="styleGuidePrompt"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Seu Guia de Estilo Personalizado</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Ex: Minimalista, paleta de cores pastel, sempre inclua um gato preto, texto em português"
                        rows={5}
                        {...field}
                        disabled={isSaving}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <Button
            type="submit"
            className="w-full md:w-auto"
            disabled={isSaving}
          >
            {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Salvar Alterações
          </Button>
        </form>
      </Form>
    </div>
  );
}
