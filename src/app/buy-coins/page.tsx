'use client';

import * as React from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { createPixOrder } from '@/services/paymentService';
import { packages, type Package } from '@/services/packages';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Loader2, CheckCircle, Gem, Copy, CreditCard, QrCode } from 'lucide-react';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';

export default function BuyCoinsPage() {
  const { user, profile, pagarmeCustomerId, loading: authLoading } = useAuth();
  const router = useRouter();
  const { toast } = useToast();
  
  const [selectedPackage, setSelectedPackage] = React.useState<Package | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);
  const [pixData, setPixData] = React.useState<{ qrCodeUrl: string; qrCodeCopy: string } | null>(null);
  const [paymentMethod, setPaymentMethod] = React.useState('pix');


  const isProfileComplete = React.useMemo(() => {
    if (!profile) return false;
    return (
      profile.name &&
      profile.document &&
      profile.birthdate &&
      profile.phone?.areaCode &&
      profile.phone?.number &&
      profile.address?.postalCode &&
      profile.address?.street &&
      profile.address?.number &&
      profile.address?.neighborhood &&
      profile.address?.city &&
      profile.address?.state
    );
  }, [profile]);
  
  const handleSelectPackage = (pkg: Package) => {
    if (!isProfileComplete) {
      toast({
        title: 'Perfil Incompleto',
        description: 'Por favor, complete seu perfil antes de fazer uma compra.',
        variant: 'destructive',
      });
      router.push('/profile');
      return;
    }
    setSelectedPackage(pkg);
    setPixData(null); // Reset PIX data when opening dialog
  };

  const handleCreateOrder = async () => {
    if (!user || !profile || !selectedPackage) return;
    
    setIsLoading(true);
    try {
      let result;
      if (paymentMethod === 'pix') {
         result = await createPixOrder(user.uid, selectedPackage.id, profile, user.email!, pagarmeCustomerId ?? undefined);
      } else {
        // Logic for credit card payment will go here
        toast({ title: "Método não implementado", description: "Pagamento com cartão de crédito em breve.", variant: "destructive" });
        setIsLoading(false);
        return;
      }
      
      if (result.success && result.qrCodeUrl && result.qrCodeCopy) {
        setPixData({ qrCodeUrl: result.qrCodeUrl, qrCodeCopy: result.qrCodeCopy });
      } else {
        throw new Error(result.error || 'Não foi possível gerar o pedido.');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Ocorreu um erro desconhecido.';
      toast({
        title: 'Falha ao Gerar Pedido',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({ title: 'Copiado!', description: 'O código PIX Copia e Cola foi copiado para sua área de transferência.' });
  };
  
  if (authLoading) {
    return (
       <div className="container mx-auto px-4 py-12">
        <div className="text-center">Carregando...</div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="container mx-auto flex h-[calc(100vh-8rem)] items-center justify-center px-4 py-12">
        <Alert>
          <Gem className="h-4 w-4" />
          <AlertTitle>Faça Login para Continuar</AlertTitle>
          <AlertDescription>
            Você precisa{' '}
            <Link href="/login" className="font-bold underline">
              fazer login
            </Link>{' '}
            para comprar MuseCoins e continuar criando.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <>
      <div className="container mx-auto px-4 py-12">
        <header className="text-center mb-12">
          <h1 className="font-headline text-5xl md:text-6xl font-bold tracking-tighter">
            Comprar MuseCoins
          </h1>
          <p className="text-muted-foreground mt-2 text-lg max-w-2xl mx-auto">
            Escolha um pacote e continue criando pôsteres incríveis.
          </p>
        </header>
        {!isProfileComplete && (
            <Alert variant="destructive" className="mb-8 max-w-4xl mx-auto">
              <Gem className="h-4 w-4" />
              <AlertTitle>Complete seu Perfil</AlertTitle>
              <AlertDescription>
                Seu perfil de pagamento está incompleto. Por favor,{' '}
                <Button variant="link" asChild className="p-0 h-auto text-destructive-foreground underline"><Link href="/profile">preencha seus dados</Link></Button>
                {' '}para poder realizar compras.
              </AlertDescription>
            </Alert>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {packages.map((pkg) => {
            const isRecommended = pkg.id === 'pro';
            const images = pkg.coins / 10;
            return (
              <Card
                key={pkg.id}
                className={cn(
                  'flex flex-col shadow-lg',
                  isRecommended && 'border-primary ring-2 ring-primary'
                )}
              >
                <CardHeader className="relative">
                  {isRecommended && (
                    <Badge
                      className="absolute top-0 right-4 -translate-y-1/2"
                      variant="default"
                    >
                      Recomendado
                    </Badge>
                  )}
                  <CardTitle className="font-headline text-3xl">{pkg.name}</CardTitle>
                  <CardDescription className="pt-1">{pkg.description}</CardDescription>
                </CardHeader>
                <CardContent className="flex-grow space-y-4">
                  <div className="text-5xl font-bold">
                    {pkg.price}
                    <span className="text-lg font-normal text-muted-foreground"> BRL</span>
                  </div>
                  <div className="space-y-2 text-sm text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <Gem className="h-4 w-4 text-amber-400" />
                      <span>
                        <span className="font-bold text-foreground">{pkg.coins.toLocaleString('pt-BR')}</span> MuseCoins
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span>
                        Gere até{' '}
                        <span className="font-bold text-foreground">{images.toLocaleString('pt-BR')}</span> imagens
                      </span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button
                    className="w-full text-lg"
                    size="lg"
                    onClick={() => handleSelectPackage(pkg)}
                    disabled={!isProfileComplete}
                  >
                    Comprar Agora
                  </Button>
                </CardFooter>
              </Card>
            );
          })}
        </div>
        <div className="text-center mt-12 text-sm text-muted-foreground">
            <p>Pagamentos processados com segurança por Pagar.me e Mercado Pago.</p>
        </div>
      </div>

      <Dialog open={!!selectedPackage} onOpenChange={(open) => !open && setSelectedPackage(null)}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Finalizar Compra</DialogTitle>
            <DialogDescription>
              Você está comprando o pacote <strong>{selectedPackage?.name}</strong> por <strong>{selectedPackage?.price}</strong>.
            </DialogDescription>
          </DialogHeader>
          
          {pixData ? (
            <div className="py-4 space-y-4 text-center">
              <h3 className="font-semibold">Pague com PIX para continuar</h3>
              <p className="text-sm text-muted-foreground">
                Escaneie o QR Code com o app do seu banco. O pagamento será confirmado em instantes.
              </p>
              <div className="p-4 bg-white rounded-lg inline-block">
                <Image src={pixData.qrCodeUrl} alt="PIX QR Code" width={200} height={200} />
              </div>
              <Button onClick={() => copyToClipboard(pixData.qrCodeCopy)} variant="outline" className="w-full">
                <Copy className="mr-2 h-4 w-4" />
                Copiar Código PIX
              </Button>
              <p className="text-xs text-muted-foreground pt-2">Após o pagamento, suas moedas serão creditadas automaticamente. Você pode fechar esta janela.</p>
            </div>
          ) : (
            <>
              <div className="py-4 space-y-4">
                <Label>Selecione o método de pagamento:</Label>
                <RadioGroup 
                  value={paymentMethod} 
                  onValueChange={setPaymentMethod} 
                  className="gap-4"
                >
                  <Label htmlFor="pix" className="flex items-center gap-4 rounded-md border p-4 cursor-pointer hover:bg-muted/50">
                    <RadioGroupItem value="pix" id="pix" />
                    <div className="flex items-center gap-2 font-semibold">
                      <QrCode /> PIX
                    </div>
                  </Label>
                  <Label htmlFor="card" className="flex items-center gap-4 rounded-md border p-4 cursor-not-allowed opacity-50">
                    <RadioGroupItem value="card" id="card" disabled />
                    <div className="flex items-center gap-2 font-semibold">
                      <CreditCard /> Cartão de Crédito
                    </div>
                    <Badge variant="outline" className="ml-auto">Em breve</Badge>
                  </Label>
                </RadioGroup>
              </div>
              <DialogFooter>
                <Button onClick={handleCreateOrder} className="w-full" disabled={isLoading}>
                  {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Confirmar e Gerar Pagamento
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
