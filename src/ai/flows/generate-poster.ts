// src/ai/flows/generate-poster.ts
'use server';
/**
 * @fileOverview A flow for generating posters based on a text prompt and an optional image.
 *
 * - generatePoster - A function that generates a poster based on the provided input.
 * - GeneratePosterInput - The input type for the generatePoster function.
 * - GeneratePosterOutput - The return type for the generatePoster function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const GeneratePosterInputSchema = z.object({
  prompt: z.string().describe('The text prompt for generating the poster.'),
  image: z
    .string()
    .optional()
    .describe(
      "An optional image to guide the poster generation, as a data URI that must include a MIME type and use Base64 encoding. Expected format: 'data:<mimetype>;base64,<encoded_data>'."
    ),
  aspectRatio: z
    .string()
    .optional()
    .describe("The desired aspect ratio for the poster (e.g., '1:1', '16:9')."),
  modelId: z.string().describe('The ID of the AI model to use for generation.'),
  styleGuidePrompt: z
    .string()
    .optional()
    .describe('General style instructions to always apply to the generation.'),
});
export type GeneratePosterInput = z.infer<typeof GeneratePosterInputSchema>;

const GeneratePosterOutputSchema = z.object({
  posterUrl: z.string().describe('The URL of the generated poster image.'),
});
export type GeneratePosterOutput = z.infer<typeof GeneratePosterOutputSchema>;

export async function generatePoster(
  input: GeneratePosterInput
): Promise<GeneratePosterOutput> {
  return generatePosterFlow(input);
}

const generatePosterPrompt = ai.definePrompt({
  name: 'generatePosterPrompt',
  input: {schema: GeneratePosterInputSchema},
  prompt: `You are an AI that generates posters based on user prompts. Create a high-quality poster based on the prompt below.
{{#if styleGuidePrompt}}
Always follow these general style instructions for all generated images: {{{styleGuidePrompt}}}
{{/if}}
{{#if aspectRatio}}
The desired aspect ratio is {{aspectRatio}}.
{{/if}}

Main Prompt: {{{prompt}}}

{{#if image}}
Use the following image as a style reference: {{media url=image}}
{{/if}}`,
});

const generatePosterFlow = ai.defineFlow(
  {
    name: 'generatePosterFlow',
    inputSchema: GeneratePosterInputSchema,
    outputSchema: GeneratePosterOutputSchema,
  },
  async (input) => {
    let promptParts: (
      | {text: string}
      | {media: {url: string; contentType?: string}}
    )[] = [];

    // Manually build the prompt parts to handle multimodal input correctly
    let textualPrompt = `Create a high-quality poster.`;

    if (input.styleGuidePrompt) {
      textualPrompt += `\nAlways follow these general style instructions: ${input.styleGuidePrompt}`;
    }
    if (input.aspectRatio) {
      textualPrompt += `\nThe desired aspect ratio is ${input.aspectRatio}.`;
    }

    textualPrompt += `\nMain Prompt: ${input.prompt}`;
    
    promptParts.push({text: textualPrompt});
    
    if (input.image) {
       promptParts.push({text: "\nUse the following image as a style reference:"});
       promptParts.push({media: {url: input.image}});
    }

    const {media} = await ai.generate({
      model: input.modelId as any,
      prompt: promptParts,
      config: {
        responseModalities: ['TEXT', 'IMAGE'],
      },
    });

    if (!media?.url) {
      throw new Error('Failed to generate poster image.');
    }

    return {posterUrl: media.url};
  }
);
