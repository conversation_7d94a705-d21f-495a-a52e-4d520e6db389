export type ImageModel = {
  id: string; // Unique ID for UI controls
  modelId: string; // Actual model ID for the API call
  name: string;
  cost: number;
  description: string;
};

export const imageModels: ImageModel[] = [
  {
    id: 'gemini-flash',
    modelId: 'googleai/gemini-2.0-flash-preview-image-generation',
    name: 'Gemini 2.0 Flash',
    cost: 10,
    description: 'Rápid<PERSON> e eficiente, ótimo para a maioria das criações.',
  },
  {
    id: 'gemini-pro-hd',
    modelId: 'googleai/gemini-2.0-flash-preview-image-generation',
    name: 'Gemini 2.0 Pro HD',
    cost: 25,
    description: '<PERSON>or qualidade e detalhes, ideal para impressões.',
  },
];
